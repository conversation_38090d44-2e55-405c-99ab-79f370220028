<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS3 Fundamentals - Web Development Manual</title>
    <link rel="stylesheet" href="assets/css/manual-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Demo styles for this page */
        .css-demo-box {
            width: 200px;
            height: 100px;
            background: linear-gradient(45deg, #3498db, #9b59b6);
            margin: 1rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .css-demo-box:hover {
            transform: scale(1.05) rotate(2deg);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .flexbox-demo {
            display: flex;
            gap: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .flex-item {
            background: #007bff;
            color: white;
            padding: 1rem;
            border-radius: 4px;
            text-align: center;
        }
        
        .grid-demo {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .grid-item {
            background: #28a745;
            color: white;
            padding: 1rem;
            border-radius: 4px;
            text-align: center;
        }
        
        .animation-demo {
            width: 100px;
            height: 100px;
            background: #ff6b6b;
            border-radius: 50%;
            animation: bounce 2s infinite;
            margin: 2rem auto;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-30px); }
            60% { transform: translateY(-15px); }
        }
        
        .variable-demo {
            --primary-color: #3498db;
            --secondary-color: #e74c3c;
            --border-radius: 8px;
            
            background: var(--primary-color);
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius);
            border: 3px solid var(--secondary-color);
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <nav class="manual-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <h2>Web Dev Manual</h2>
            </div>
            <ul class="nav-links">
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#selectors">Selectors</a></li>
                <li><a href="#box-model">Box Model</a></li>
                <li><a href="#layouts">Layouts</a></li>
                <li><a href="#animations">Animations</a></li>
                <li><a href="#modern-features">Modern CSS</a></li>
                <li><a href="#resources">Resources</a></li>
            </ul>
            <button class="theme-toggle" aria-label="Toggle theme">🌙</button>
        </div>
    </nav>

    <main class="manual-content">
        <header class="topic-header" id="introduction">
            <div class="header-content">
                <h1>CSS3 Fundamentals</h1>
                <p class="header-description">
                    Master modern CSS3 features including selectors, layouts, animations, and cutting-edge properties. 
                    Learn to create beautiful, responsive designs with the latest CSS techniques.
                </p>
                <div class="header-stats">
                    <span class="stat">🎨 Visual Design</span>
                    <span class="stat">📱 Responsive Layouts</span>
                    <span class="stat">✨ Modern Features</span>
                </div>
            </div>
        </header>

        <section class="concepts" id="selectors">
            <h2>CSS Selectors and Specificity</h2>
            <p>Understanding CSS selectors is crucial for targeting elements effectively and managing specificity.</p>
            
            <div class="demo-container">
                <h3>Selector Types</h3>
                <div class="code-block">
                    <pre><code>/* Element Selector */
p { color: blue; }

/* Class Selector */
.highlight { background-color: yellow; }

/* ID Selector */
#header { font-size: 2rem; }

/* Attribute Selector */
input[type="email"] { border: 2px solid green; }

/* Pseudo-class Selectors */
a:hover { color: red; }
li:nth-child(odd) { background-color: #f0f0f0; }
input:focus { outline: 2px solid blue; }

/* Pseudo-element Selectors */
p::first-line { font-weight: bold; }
p::before { content: "→ "; }

/* Combinators */
div > p { margin-left: 20px; } /* Direct child */
h2 + p { margin-top: 0; } /* Adjacent sibling */
h2 ~ p { color: gray; } /* General sibling */
article p { line-height: 1.6; } /* Descendant */

/* Advanced Selectors */
:not(.excluded) { display: block; }
:has(img) { border: 1px solid #ccc; }
:where(.card, .panel) { padding: 1rem; }</code></pre>
                </div>
                <button class="copy-btn" data-copy="selectors">Copy Code</button>
            </div>

            <div class="demo-container">
                <h3>Specificity Calculator</h3>
                <div class="live-demo">
                    <div class="specificity-demo">
                        <p class="demo-text" id="specificity-target">This text demonstrates CSS specificity</p>
                        <div class="specificity-controls">
                            <button onclick="applyStyle('element')">Element (0,0,0,1)</button>
                            <button onclick="applyStyle('class')">Class (0,0,1,0)</button>
                            <button onclick="applyStyle('id')">ID (0,1,0,0)</button>
                            <button onclick="applyStyle('inline')">Inline (1,0,0,0)</button>
                            <button onclick="resetStyles()">Reset</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="examples" id="box-model">
            <h2>CSS Box Model</h2>
            <p>The CSS box model describes how elements are rendered with content, padding, border, and margin.</p>
            
            <div class="demo-container">
                <h3>Box Model Visualization</h3>
                <div class="live-demo">
                    <div class="box-model-demo">
                        <div class="box-model-container">
                            <div class="margin-area">
                                <span class="label">Margin</span>
                                <div class="border-area">
                                    <span class="label">Border</span>
                                    <div class="padding-area">
                                        <span class="label">Padding</span>
                                        <div class="content-area">
                                            <span class="label">Content</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="box-model-controls">
                            <label>Box Sizing: 
                                <select id="box-sizing-select">
                                    <option value="content-box">content-box</option>
                                    <option value="border-box">border-box</option>
                                </select>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="demo-container">
                <h3>Box Model CSS</h3>
                <div class="code-block">
                    <pre><code>/* Traditional Box Model (content-box) */
.content-box {
    box-sizing: content-box; /* default */
    width: 200px;
    padding: 20px;
    border: 5px solid #333;
    margin: 10px;
    /* Total width = 200px + 40px + 10px + 20px = 270px */
}

/* Modern Box Model (border-box) */
.border-box {
    box-sizing: border-box;
    width: 200px;
    padding: 20px;
    border: 5px solid #333;
    margin: 10px;
    /* Total width = 200px (includes padding and border) */
}

/* Universal Border Box */
*, *::before, *::after {
    box-sizing: border-box;
}</code></pre>
                </div>
            </div>
        </section>

        <section class="advanced" id="layouts">
            <h2>Modern CSS Layouts</h2>
            <p>CSS provides powerful layout systems including Flexbox and Grid for creating responsive designs.</p>
            
            <div class="demo-container">
                <h3>Flexbox Layout</h3>
                <div class="live-demo">
                    <div class="flexbox-demo" id="flex-container">
                        <div class="flex-item">Item 1</div>
                        <div class="flex-item">Item 2</div>
                        <div class="flex-item">Item 3</div>
                    </div>
                    <div class="layout-controls">
                        <label>Justify Content: 
                            <select id="justify-content">
                                <option value="flex-start">flex-start</option>
                                <option value="center">center</option>
                                <option value="flex-end">flex-end</option>
                                <option value="space-between">space-between</option>
                                <option value="space-around">space-around</option>
                                <option value="space-evenly">space-evenly</option>
                            </select>
                        </label>
                        <label>Align Items: 
                            <select id="align-items">
                                <option value="stretch">stretch</option>
                                <option value="flex-start">flex-start</option>
                                <option value="center">center</option>
                                <option value="flex-end">flex-end</option>
                            </select>
                        </label>
                        <label>Direction: 
                            <select id="flex-direction">
                                <option value="row">row</option>
                                <option value="column">column</option>
                                <option value="row-reverse">row-reverse</option>
                                <option value="column-reverse">column-reverse</option>
                            </select>
                        </label>
                    </div>
                </div>
            </div>

            <div class="demo-container">
                <h3>CSS Grid Layout</h3>
                <div class="live-demo">
                    <div class="grid-demo" id="grid-container">
                        <div class="grid-item">1</div>
                        <div class="grid-item">2</div>
                        <div class="grid-item">3</div>
                        <div class="grid-item">4</div>
                        <div class="grid-item">5</div>
                        <div class="grid-item">6</div>
                    </div>
                    <div class="layout-controls">
                        <label>Columns: 
                            <select id="grid-columns">
                                <option value="repeat(3, 1fr)">3 equal columns</option>
                                <option value="1fr 2fr 1fr">1-2-1 ratio</option>
                                <option value="repeat(auto-fit, minmax(100px, 1fr))">Auto-fit</option>
                                <option value="200px 1fr 100px">Fixed-Flexible-Fixed</option>
                            </select>
                        </label>
                        <label>Gap: 
                            <input type="range" id="grid-gap" min="0" max="30" value="16">
                            <span id="gap-value">16px</span>
                        </label>
                    </div>
                </div>
            </div>
        </section>

        <section class="best-practices" id="animations">
            <h2>CSS Animations and Transitions</h2>
            <p>Create smooth, performant animations using CSS transitions and keyframe animations.</p>
            
            <div class="demo-container">
                <h3>CSS Transitions</h3>
                <div class="code-block">
                    <pre><code>/* Basic Transition */
.button {
    background-color: #3498db;
    transition: background-color 0.3s ease;
}

.button:hover {
    background-color: #2980b9;
}

/* Multiple Properties */
.card {
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>CSS Keyframe Animations</h3>
                <div class="live-demo">
                    <div class="animation-demo"></div>
                    <div class="animation-controls">
                        <button onclick="toggleAnimation()">Toggle Animation</button>
                        <button onclick="changeAnimation('spin')">Spin</button>
                        <button onclick="changeAnimation('pulse')">Pulse</button>
                        <button onclick="changeAnimation('bounce')">Bounce</button>
                    </div>
                </div>
                
                <div class="code-block">
                    <pre><code>@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-30px); }
    60% { transform: translateY(-15px); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.animated {
    animation: bounce 2s infinite;
}</code></pre>
                </div>
            </div>
        </section>

        <section class="modern-features" id="modern-features">
            <h2>Modern CSS Features</h2>
            <p>Explore cutting-edge CSS features including custom properties, container queries, and more.</p>
            
            <div class="demo-container">
                <h3>CSS Custom Properties (Variables)</h3>
                <div class="live-demo">
                    <div class="variable-demo" id="variable-demo">
                        CSS Custom Properties Demo
                    </div>
                    <div class="variable-controls">
                        <label>Primary Color: 
                            <input type="color" id="primary-color" value="#3498db">
                        </label>
                        <label>Secondary Color: 
                            <input type="color" id="secondary-color" value="#e74c3c">
                        </label>
                        <label>Border Radius: 
                            <input type="range" id="border-radius" min="0" max="50" value="8">
                            <span id="radius-value">8px</span>
                        </label>
                    </div>
                </div>
                
                <div class="code-block">
                    <pre><code>:root {
    --primary-color: #3498db;
    --secondary-color: #e74c3c;
    --border-radius: 8px;
    --spacing-unit: 1rem;
}

.component {
    background: var(--primary-color);
    border: 2px solid var(--secondary-color);
    border-radius: var(--border-radius);
    padding: calc(var(--spacing-unit) * 2);
}

/* Dynamic theming */
[data-theme="dark"] {
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
}</code></pre>
                </div>
            </div>
        </section>

        <section class="resources" id="resources">
            <h2>Additional Resources</h2>
            <div class="resource-grid">
                <div class="resource-card">
                    <h3>📖 Official Documentation</h3>
                    <ul>
                        <li><a href="https://developer.mozilla.org/en-US/docs/Web/CSS" target="_blank">MDN CSS Reference</a></li>
                        <li><a href="https://www.w3.org/Style/CSS/" target="_blank">W3C CSS Specifications</a></li>
                        <li><a href="https://css-tricks.com/" target="_blank">CSS-Tricks</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>🛠️ Tools & Generators</h3>
                    <ul>
                        <li><a href="https://flexboxfroggy.com/" target="_blank">Flexbox Froggy</a></li>
                        <li><a href="https://cssgrid.io/" target="_blank">CSS Grid Course</a></li>
                        <li><a href="https://animista.net/" target="_blank">Animista</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>📚 Learning Resources</h3>
                    <ul>
                        <li><a href="https://web.dev/learn/css/" target="_blank">Web.dev CSS Course</a></li>
                        <li><a href="https://cssreference.io/" target="_blank">CSS Reference</a></li>
                        <li><a href="https://every-layout.dev/" target="_blank">Every Layout</a></li>
                    </ul>
                </div>
            </div>
        </section>
    </main>

    <script src="assets/js/manual-scripts.js"></script>
    <script>
        // Specificity demo
        function applyStyle(type) {
            const target = document.getElementById('specificity-target');
            target.className = 'demo-text';
            target.removeAttribute('style');
            
            switch(type) {
                case 'element':
                    target.style.cssText = '';
                    document.head.insertAdjacentHTML('beforeend', '<style id="temp-style">p { color: red !important; }</style>');
                    break;
                case 'class':
                    target.className = 'demo-text highlight-class';
                    document.head.insertAdjacentHTML('beforeend', '<style id="temp-style">.highlight-class { color: blue !important; }</style>');
                    break;
                case 'id':
                    target.id = 'specificity-target';
                    document.head.insertAdjacentHTML('beforeend', '<style id="temp-style">#specificity-target { color: green !important; }</style>');
                    break;
                case 'inline':
                    target.style.color = 'purple';
                    break;
            }
        }
        
        function resetStyles() {
            const target = document.getElementById('specificity-target');
            target.className = 'demo-text';
            target.removeAttribute('style');
            const tempStyle = document.getElementById('temp-style');
            if (tempStyle) tempStyle.remove();
        }
        
        // Flexbox controls
        document.getElementById('justify-content').addEventListener('change', function() {
            document.getElementById('flex-container').style.justifyContent = this.value;
        });
        
        document.getElementById('align-items').addEventListener('change', function() {
            document.getElementById('flex-container').style.alignItems = this.value;
        });
        
        document.getElementById('flex-direction').addEventListener('change', function() {
            document.getElementById('flex-container').style.flexDirection = this.value;
        });
        
        // Grid controls
        document.getElementById('grid-columns').addEventListener('change', function() {
            document.getElementById('grid-container').style.gridTemplateColumns = this.value;
        });
        
        document.getElementById('grid-gap').addEventListener('input', function() {
            document.getElementById('grid-container').style.gap = this.value + 'px';
            document.getElementById('gap-value').textContent = this.value + 'px';
        });
        
        // Animation controls
        let isAnimating = true;
        function toggleAnimation() {
            const demo = document.querySelector('.animation-demo');
            isAnimating = !isAnimating;
            demo.style.animationPlayState = isAnimating ? 'running' : 'paused';
        }
        
        function changeAnimation(type) {
            const demo = document.querySelector('.animation-demo');
            demo.style.animation = `${type} 2s infinite`;
        }
        
        // CSS Variables controls
        document.getElementById('primary-color').addEventListener('input', function() {
            document.getElementById('variable-demo').style.setProperty('--primary-color', this.value);
        });
        
        document.getElementById('secondary-color').addEventListener('input', function() {
            document.getElementById('variable-demo').style.setProperty('--secondary-color', this.value);
        });
        
        document.getElementById('border-radius').addEventListener('input', function() {
            document.getElementById('variable-demo').style.setProperty('--border-radius', this.value + 'px');
            document.getElementById('radius-value').textContent = this.value + 'px';
        });
    </script>
    
    <style>
        /* Additional demo styles */
        .box-model-container {
            max-width: 400px;
            margin: 2rem auto;
        }
        
        .margin-area {
            background: #ffeb3b;
            padding: 20px;
            position: relative;
        }
        
        .border-area {
            background: #ff9800;
            padding: 15px;
            position: relative;
        }
        
        .padding-area {
            background: #4caf50;
            padding: 20px;
            position: relative;
        }
        
        .content-area {
            background: #2196f3;
            padding: 30px;
            color: white;
            text-align: center;
            position: relative;
        }
        
        .label {
            position: absolute;
            top: 5px;
            left: 5px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .layout-controls, .animation-controls, .variable-controls {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .layout-controls label, .animation-controls label, .variable-controls label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
    </style>
</body>
</html>
