<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML5 Fundamentals - Web Development Manual</title>
    <link rel="stylesheet" href="assets/css/manual-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <nav class="manual-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <h2>Web Dev Manual</h2>
            </div>
            <ul class="nav-links">
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#semantic-elements">Semantic Elements</a></li>
                <li><a href="#forms">Forms</a></li>
                <li><a href="#media">Media</a></li>
                <li><a href="#accessibility">Accessibility</a></li>
                <li><a href="#best-practices">Best Practices</a></li>
                <li><a href="#resources">Resources</a></li>
            </ul>
            <button class="theme-toggle" aria-label="Toggle theme">🌙</button>
        </div>
    </nav>

    <main class="manual-content">
        <header class="topic-header" id="introduction">
            <div class="header-content">
                <h1>HTML5 Fundamentals</h1>
                <p class="header-description">
                    Master the foundation of web development with HTML5 semantic elements, forms, media, and accessibility features. 
                    This comprehensive guide covers everything from basic structure to advanced HTML5 APIs.
                </p>
                <div class="header-stats">
                    <span class="stat">📚 Complete Reference</span>
                    <span class="stat">🎯 Interactive Examples</span>
                    <span class="stat">♿ Accessibility Focused</span>
                </div>
            </div>
        </header>

        <section class="concepts" id="semantic-elements">
            <h2>HTML5 Semantic Elements</h2>
            <p>HTML5 introduced semantic elements that provide meaning to the structure of web content, improving accessibility and SEO.</p>
            
            <div class="demo-container">
                <h3>Document Structure</h3>
                <div class="code-block">
                    <pre><code>&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;Semantic HTML5 Document&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;header&gt;
        &lt;nav&gt;
            &lt;ul&gt;
                &lt;li&gt;&lt;a href="#home"&gt;Home&lt;/a&gt;&lt;/li&gt;
                &lt;li&gt;&lt;a href="#about"&gt;About&lt;/a&gt;&lt;/li&gt;
                &lt;li&gt;&lt;a href="#contact"&gt;Contact&lt;/a&gt;&lt;/li&gt;
            &lt;/ul&gt;
        &lt;/nav&gt;
    &lt;/header&gt;
    
    &lt;main&gt;
        &lt;article&gt;
            &lt;header&gt;
                &lt;h1&gt;Article Title&lt;/h1&gt;
                &lt;time datetime="2024-01-15"&gt;January 15, 2024&lt;/time&gt;
            &lt;/header&gt;
            &lt;section&gt;
                &lt;h2&gt;Section Heading&lt;/h2&gt;
                &lt;p&gt;Article content goes here...&lt;/p&gt;
            &lt;/section&gt;
        &lt;/article&gt;
        
        &lt;aside&gt;
            &lt;h3&gt;Related Links&lt;/h3&gt;
            &lt;ul&gt;
                &lt;li&gt;&lt;a href="#"&gt;Related Article 1&lt;/a&gt;&lt;/li&gt;
                &lt;li&gt;&lt;a href="#"&gt;Related Article 2&lt;/a&gt;&lt;/li&gt;
            &lt;/ul&gt;
        &lt;/aside&gt;
    &lt;/main&gt;
    
    &lt;footer&gt;
        &lt;p&gt;&copy; 2024 Your Website. All rights reserved.&lt;/p&gt;
    &lt;/footer&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
                </div>
                <button class="copy-btn" data-copy="semantic-structure">Copy Code</button>
            </div>

            <div class="demo-container">
                <h3>Live Example: Semantic Structure</h3>
                <div class="live-demo" id="semantic-demo">
                    <header style="background: #e3f2fd; padding: 1rem; margin-bottom: 1rem;">
                        <h2>Website Header</h2>
                        <nav>
                            <a href="#" style="margin-right: 1rem;">Home</a>
                            <a href="#" style="margin-right: 1rem;">About</a>
                            <a href="#" style="margin-right: 1rem;">Contact</a>
                        </nav>
                    </header>
                    
                    <main style="display: flex; gap: 1rem;">
                        <article style="flex: 2; background: #f3e5f5; padding: 1rem;">
                            <header>
                                <h3>Article Title</h3>
                                <time datetime="2024-01-15">January 15, 2024</time>
                            </header>
                            <section>
                                <h4>Section Heading</h4>
                                <p>This is the main content of the article. It demonstrates how semantic elements structure content meaningfully.</p>
                            </section>
                        </article>
                        
                        <aside style="flex: 1; background: #e8f5e8; padding: 1rem;">
                            <h4>Sidebar</h4>
                            <ul>
                                <li>Related Link 1</li>
                                <li>Related Link 2</li>
                                <li>Related Link 3</li>
                            </ul>
                        </aside>
                    </main>
                    
                    <footer style="background: #fff3e0; padding: 1rem; margin-top: 1rem; text-align: center;">
                        <p>&copy; 2024 Semantic HTML Example</p>
                    </footer>
                </div>
            </div>
        </section>

        <section class="examples" id="forms">
            <h2>HTML5 Forms and Input Types</h2>
            <p>HTML5 introduced new input types and form validation features that enhance user experience and data collection.</p>
            
            <div class="demo-container">
                <h3>Modern Form Elements</h3>
                <div class="live-demo">
                    <form class="modern-form" id="html5-form">
                        <fieldset>
                            <legend>Personal Information</legend>
                            
                            <div class="form-group">
                                <label for="name">Full Name *</label>
                                <input type="text" id="name" name="name" required placeholder="Enter your full name">
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                            </div>
                            
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" id="phone" name="phone" placeholder="+****************">
                            </div>
                            
                            <div class="form-group">
                                <label for="birthdate">Date of Birth</label>
                                <input type="date" id="birthdate" name="birthdate">
                            </div>
                            
                            <div class="form-group">
                                <label for="website">Website</label>
                                <input type="url" id="website" name="website" placeholder="https://example.com">
                            </div>
                            
                            <div class="form-group">
                                <label for="age">Age</label>
                                <input type="number" id="age" name="age" min="18" max="120" placeholder="25">
                            </div>
                            
                            <div class="form-group">
                                <label for="experience">Experience Level</label>
                                <input type="range" id="experience" name="experience" min="0" max="10" value="5">
                                <output for="experience">5</output>
                            </div>
                            
                            <div class="form-group">
                                <label for="color-preference">Favorite Color</label>
                                <input type="color" id="color-preference" name="color-preference" value="#3498db">
                            </div>
                        </fieldset>
                        
                        <fieldset>
                            <legend>Preferences</legend>
                            
                            <div class="form-group">
                                <label for="bio">Bio</label>
                                <textarea id="bio" name="bio" rows="4" placeholder="Tell us about yourself..."></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="country">Country</label>
                                <select id="country" name="country">
                                    <option value="">Select a country</option>
                                    <option value="us">United States</option>
                                    <option value="ca">Canada</option>
                                    <option value="uk">United Kingdom</option>
                                    <option value="de">Germany</option>
                                    <option value="fr">France</option>
                                </select>
                            </div>
                            
                            <div class="form-group checkbox-group">
                                <input type="checkbox" id="newsletter" name="newsletter">
                                <label for="newsletter">Subscribe to newsletter</label>
                            </div>
                            
                            <div class="form-group radio-group">
                                <fieldset>
                                    <legend>Preferred Contact Method</legend>
                                    <input type="radio" id="contact-email" name="contact-method" value="email">
                                    <label for="contact-email">Email</label>
                                    
                                    <input type="radio" id="contact-phone" name="contact-method" value="phone">
                                    <label for="contact-phone">Phone</label>
                                    
                                    <input type="radio" id="contact-mail" name="contact-method" value="mail">
                                    <label for="contact-mail">Mail</label>
                                </fieldset>
                            </div>
                        </fieldset>
                        
                        <div class="form-actions">
                            <button type="submit">Submit Form</button>
                            <button type="reset">Reset Form</button>
                        </div>
                    </form>
                </div>
            </div>
        </section>

        <section class="advanced" id="media">
            <h2>HTML5 Media Elements</h2>
            <p>HTML5 provides native support for audio, video, and graphics through various media elements.</p>
            
            <div class="demo-container">
                <h3>Video Element</h3>
                <div class="code-block">
                    <pre><code>&lt;video controls width="100%" height="300"&gt;
    &lt;source src="video.mp4" type="video/mp4"&gt;
    &lt;source src="video.webm" type="video/webm"&gt;
    &lt;track kind="subtitles" src="subtitles.vtt" srclang="en" label="English"&gt;
    Your browser does not support the video tag.
&lt;/video&gt;</code></pre>
                </div>
            </div>
            
            <div class="demo-container">
                <h3>Audio Element</h3>
                <div class="code-block">
                    <pre><code>&lt;audio controls&gt;
    &lt;source src="audio.mp3" type="audio/mpeg"&gt;
    &lt;source src="audio.ogg" type="audio/ogg"&gt;
    Your browser does not support the audio element.
&lt;/audio&gt;</code></pre>
                </div>
            </div>
            
            <div class="demo-container">
                <h3>Canvas Element</h3>
                <div class="live-demo">
                    <canvas id="demo-canvas" width="400" height="200" style="border: 1px solid #ccc;"></canvas>
                    <div style="margin-top: 1rem;">
                        <button onclick="drawCircle()">Draw Circle</button>
                        <button onclick="drawRectangle()">Draw Rectangle</button>
                        <button onclick="clearCanvas()">Clear Canvas</button>
                    </div>
                </div>
            </div>
        </section>

        <section class="best-practices" id="accessibility">
            <h2>Accessibility Best Practices</h2>
            <p>Creating accessible HTML ensures your content is usable by everyone, including users with disabilities.</p>
            
            <div class="demo-container">
                <h3>ARIA Attributes and Semantic HTML</h3>
                <div class="code-block">
                    <pre><code>&lt;!-- Use semantic elements when possible --&gt;
&lt;nav aria-label="Main navigation"&gt;
    &lt;ul&gt;
        &lt;li&gt;&lt;a href="#home" aria-current="page"&gt;Home&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href="#about"&gt;About&lt;/a&gt;&lt;/li&gt;
    &lt;/ul&gt;
&lt;/nav&gt;

&lt;!-- Form accessibility --&gt;
&lt;form&gt;
    &lt;label for="search-input"&gt;Search&lt;/label&gt;
    &lt;input type="search" id="search-input" 
           aria-describedby="search-help"
           placeholder="Enter search terms"&gt;
    &lt;div id="search-help"&gt;Search across all articles and tutorials&lt;/div&gt;
    &lt;button type="submit" aria-label="Submit search"&gt;🔍&lt;/button&gt;
&lt;/form&gt;

&lt;!-- Image accessibility --&gt;
&lt;img src="chart.png" alt="Sales increased 25% from Q1 to Q2 2024"&gt;

&lt;!-- Skip navigation link --&gt;
&lt;a href="#main-content" class="skip-link"&gt;Skip to main content&lt;/a&gt;</code></pre>
                </div>
            </div>
        </section>

        <section class="resources" id="resources">
            <h2>Additional Resources</h2>
            <div class="resource-grid">
                <div class="resource-card">
                    <h3>📖 Official Documentation</h3>
                    <ul>
                        <li><a href="https://developer.mozilla.org/en-US/docs/Web/HTML" target="_blank">MDN HTML Reference</a></li>
                        <li><a href="https://html.spec.whatwg.org/" target="_blank">HTML Living Standard</a></li>
                        <li><a href="https://www.w3.org/WAI/WCAG21/quickref/" target="_blank">WCAG 2.1 Guidelines</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>🛠️ Tools & Validators</h3>
                    <ul>
                        <li><a href="https://validator.w3.org/" target="_blank">W3C HTML Validator</a></li>
                        <li><a href="https://wave.webaim.org/" target="_blank">WAVE Accessibility Checker</a></li>
                        <li><a href="https://caniuse.com/" target="_blank">Can I Use</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>📚 Learning Resources</h3>
                    <ul>
                        <li><a href="https://web.dev/learn/html/" target="_blank">Web.dev HTML Course</a></li>
                        <li><a href="https://htmlreference.io/" target="_blank">HTML Reference</a></li>
                        <li><a href="https://a11yproject.com/" target="_blank">A11Y Project</a></li>
                    </ul>
                </div>
            </div>
        </section>
    </main>

    <script src="assets/js/manual-scripts.js"></script>
    <script>
        // Canvas demo functionality
        function drawCircle() {
            const canvas = document.getElementById('demo-canvas');
            const ctx = canvas.getContext('2d');
            ctx.beginPath();
            ctx.arc(200, 100, 50, 0, 2 * Math.PI);
            ctx.fillStyle = '#3498db';
            ctx.fill();
            ctx.stroke();
        }
        
        function drawRectangle() {
            const canvas = document.getElementById('demo-canvas');
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(150, 50, 100, 100);
            ctx.strokeRect(150, 50, 100, 100);
        }
        
        function clearCanvas() {
            const canvas = document.getElementById('demo-canvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        
        // Range input output update
        document.getElementById('experience').addEventListener('input', function() {
            document.querySelector('output[for="experience"]').textContent = this.value;
        });
        
        // Form submission handler
        document.getElementById('html5-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Form submitted successfully! (This is just a demo)');
        });
    </script>
</body>
</html>
