<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Fundamentals - Web Development Manual</title>
    <link rel="stylesheet" href="assets/css/manual-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Fira Code', monospace;
            margin: 1rem 0;
            min-height: 100px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .js-playground {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .code-editor {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Fira Code', monospace;
            border: none;
            resize: vertical;
            min-height: 200px;
        }
        
        .variable-demo {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .function-demo {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .object-demo {
            background: #f0fff4;
            border: 1px solid #c6f6d5;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <nav class="manual-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <h2>Web Dev Manual</h2>
            </div>
            <ul class="nav-links">
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#variables">Variables</a></li>
                <li><a href="#functions">Functions</a></li>
                <li><a href="#objects">Objects & Arrays</a></li>
                <li><a href="#control-flow">Control Flow</a></li>
                <li><a href="#modern-js">Modern JS</a></li>
                <li><a href="#resources">Resources</a></li>
            </ul>
            <button class="theme-toggle" aria-label="Toggle theme">🌙</button>
        </div>
    </nav>

    <main class="manual-content">
        <header class="topic-header" id="introduction">
            <div class="header-content">
                <h1>JavaScript Fundamentals</h1>
                <p class="header-description">
                    Master the core concepts of JavaScript including variables, functions, objects, and modern ES2024 features. 
                    Build a solid foundation for web development with interactive examples and practical exercises.
                </p>
                <div class="header-stats">
                    <span class="stat">⚡ Interactive Playground</span>
                    <span class="stat">🔧 Practical Examples</span>
                    <span class="stat">🚀 Modern Features</span>
                </div>
            </div>
        </header>

        <section class="concepts" id="variables">
            <h2>Variables and Data Types</h2>
            <p>JavaScript provides different ways to declare variables and supports various data types.</p>
            
            <div class="demo-container">
                <h3>Variable Declarations</h3>
                <div class="code-block">
                    <pre><code>// Variable declarations
let name = "John Doe";           // Block-scoped, can be reassigned
const age = 25;                  // Block-scoped, cannot be reassigned
var city = "New York";           // Function-scoped (avoid in modern JS)

// Data types
let string = "Hello World";      // String
let number = 42;                 // Number
let boolean = true;              // Boolean
let nullValue = null;            // Null
let undefinedValue;              // Undefined
let symbol = Symbol('id');       // Symbol
let bigInt = 123n;               // BigInt

// Objects and Arrays
let person = { name: "Alice", age: 30 };
let numbers = [1, 2, 3, 4, 5];

// Template literals
let greeting = `Hello, ${name}! You are ${age} years old.`;

console.log(typeof string);      // "string"
console.log(typeof number);      // "number"
console.log(typeof boolean);     // "boolean"</code></pre>
                </div>
                <button class="copy-btn" data-copy="variables">Copy Code</button>
            </div>

            <div class="demo-container">
                <h3>Interactive Variable Demo</h3>
                <div class="variable-demo">
                    <div class="js-playground">
                        <div>
                            <h4>Try it yourself:</h4>
                            <textarea class="code-editor" id="variable-editor" placeholder="// Try declaring variables here
let message = 'Hello World';
console.log(message);
console.log(typeof message);">// Try declaring variables here
let message = 'Hello World';
console.log(message);
console.log(typeof message);</textarea>
                            <button onclick="runVariableCode()">Run Code</button>
                        </div>
                        <div>
                            <h4>Console Output:</h4>
                            <div class="console-output" id="variable-output"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="examples" id="functions">
            <h2>Functions</h2>
            <p>Functions are reusable blocks of code that perform specific tasks. JavaScript supports various function syntaxes.</p>
            
            <div class="demo-container">
                <h3>Function Types</h3>
                <div class="code-block">
                    <pre><code>// Function Declaration
function greet(name) {
    return `Hello, ${name}!`;
}

// Function Expression
const add = function(a, b) {
    return a + b;
};

// Arrow Functions
const multiply = (a, b) => a * b;
const square = x => x * x;
const sayHello = () => console.log("Hello!");

// Higher-order Functions
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
const evens = numbers.filter(n => n % 2 === 0);
const sum = numbers.reduce((acc, n) => acc + n, 0);

// Default Parameters
function createUser(name, age = 18, role = 'user') {
    return { name, age, role };
}

// Rest Parameters
function sum(...numbers) {
    return numbers.reduce((total, num) => total + num, 0);
}

// Destructuring Parameters
function displayUser({ name, age, email }) {
    console.log(`${name} (${age}) - ${email}`);
}</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Function Playground</h3>
                <div class="function-demo">
                    <div class="js-playground">
                        <div>
                            <h4>Function Examples:</h4>
                            <textarea class="code-editor" id="function-editor">// Try different function types
const calculator = {
    add: (a, b) => a + b,
    subtract: (a, b) => a - b,
    multiply: (a, b) => a * b,
    divide: (a, b) => b !== 0 ? a / b : 'Cannot divide by zero'
};

console.log(calculator.add(5, 3));
console.log(calculator.multiply(4, 7));

// Array methods
const fruits = ['apple', 'banana', 'orange'];
console.log(fruits.map(fruit => fruit.toUpperCase()));</textarea>
                            <button onclick="runFunctionCode()">Run Code</button>
                        </div>
                        <div>
                            <h4>Console Output:</h4>
                            <div class="console-output" id="function-output"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="advanced" id="objects">
            <h2>Objects and Arrays</h2>
            <p>Objects and arrays are fundamental data structures in JavaScript for organizing and manipulating data.</p>
            
            <div class="demo-container">
                <h3>Object Operations</h3>
                <div class="code-block">
                    <pre><code>// Object creation and manipulation
const person = {
    name: "Alice",
    age: 30,
    city: "New York",
    hobbies: ["reading", "coding", "hiking"]
};

// Accessing properties
console.log(person.name);        // Dot notation
console.log(person["age"]);      // Bracket notation

// Adding/modifying properties
person.email = "<EMAIL>";
person.age = 31;

// Object methods
const user = {
    firstName: "John",
    lastName: "Doe",
    fullName() {
        return `${this.firstName} ${this.lastName}`;
    },
    greet: function() {
        return `Hello, I'm ${this.fullName()}`;
    }
};

// Object destructuring
const { name, age, city } = person;
const { firstName: fName, lastName: lName } = user;

// Object.keys, Object.values, Object.entries
console.log(Object.keys(person));
console.log(Object.values(person));
console.log(Object.entries(person));</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Array Methods</h3>
                <div class="code-block">
                    <pre><code>const numbers = [1, 2, 3, 4, 5];
const fruits = ["apple", "banana", "orange"];

// Array methods
numbers.push(6);                 // Add to end
numbers.unshift(0);              // Add to beginning
numbers.pop();                   // Remove from end
numbers.shift();                 // Remove from beginning

// Functional array methods
const doubled = numbers.map(n => n * 2);
const evens = numbers.filter(n => n % 2 === 0);
const sum = numbers.reduce((acc, n) => acc + n, 0);
const found = numbers.find(n => n > 3);
const hasEven = numbers.some(n => n % 2 === 0);
const allPositive = numbers.every(n => n > 0);

// Array destructuring
const [first, second, ...rest] = numbers;

// Spread operator
const combined = [...numbers, ...fruits];
const copy = [...numbers];</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Object & Array Playground</h3>
                <div class="object-demo">
                    <div class="js-playground">
                        <div>
                            <h4>Try Objects & Arrays:</h4>
                            <textarea class="code-editor" id="object-editor">// Create and manipulate objects and arrays
const students = [
    { name: "Alice", grade: 85, subject: "Math" },
    { name: "Bob", grade: 92, subject: "Science" },
    { name: "Charlie", grade: 78, subject: "Math" }
];

// Find all Math students
const mathStudents = students.filter(s => s.subject === "Math");
console.log("Math students:", mathStudents);

// Calculate average grade
const avgGrade = students.reduce((sum, s) => sum + s.grade, 0) / students.length;
console.log("Average grade:", avgGrade);

// Get student names
const names = students.map(s => s.name);
console.log("Student names:", names);</textarea>
                            <button onclick="runObjectCode()">Run Code</button>
                        </div>
                        <div>
                            <h4>Console Output:</h4>
                            <div class="console-output" id="object-output"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="best-practices" id="control-flow">
            <h2>Control Flow</h2>
            <p>Control flow statements allow you to control the execution of your code based on conditions and loops.</p>
            
            <div class="demo-container">
                <h3>Conditional Statements</h3>
                <div class="code-block">
                    <pre><code>// If statements
const score = 85;

if (score >= 90) {
    console.log("A grade");
} else if (score >= 80) {
    console.log("B grade");
} else if (score >= 70) {
    console.log("C grade");
} else {
    console.log("Need improvement");
}

// Ternary operator
const status = score >= 60 ? "Pass" : "Fail";

// Switch statement
const day = "Monday";
switch (day) {
    case "Monday":
        console.log("Start of work week");
        break;
    case "Friday":
        console.log("TGIF!");
        break;
    case "Saturday":
    case "Sunday":
        console.log("Weekend!");
        break;
    default:
        console.log("Regular day");
}

// Nullish coalescing operator
const username = null;
const displayName = username ?? "Guest";

// Optional chaining
const user = { profile: { name: "John" } };
console.log(user.profile?.name);        // "John"
console.log(user.settings?.theme);     // undefined</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Loops</h3>
                <div class="code-block">
                    <pre><code>// For loop
for (let i = 0; i < 5; i++) {
    console.log(`Iteration ${i}`);
}

// For...of loop (for arrays)
const colors = ["red", "green", "blue"];
for (const color of colors) {
    console.log(color);
}

// For...in loop (for objects)
const person = { name: "Alice", age: 30, city: "NYC" };
for (const key in person) {
    console.log(`${key}: ${person[key]}`);
}

// While loop
let count = 0;
while (count < 3) {
    console.log(`Count: ${count}`);
    count++;
}

// Do...while loop
let num = 0;
do {
    console.log(`Number: ${num}`);
    num++;
} while (num < 3);

// Array iteration methods
const numbers = [1, 2, 3, 4, 5];
numbers.forEach((num, index) => {
    console.log(`Index ${index}: ${num}`);
});</code></pre>
                </div>
            </div>
        </section>

        <section class="modern-features" id="modern-js">
            <h2>Modern JavaScript Features</h2>
            <p>ES6+ introduced many powerful features that make JavaScript more expressive and easier to work with.</p>
            
            <div class="demo-container">
                <h3>ES6+ Features</h3>
                <div class="code-block">
                    <pre><code>// Destructuring
const [a, b, ...rest] = [1, 2, 3, 4, 5];
const { name, age, ...otherProps } = { name: "John", age: 30, city: "NYC", job: "Developer" };

// Spread operator
const arr1 = [1, 2, 3];
const arr2 = [4, 5, 6];
const combined = [...arr1, ...arr2];

const obj1 = { a: 1, b: 2 };
const obj2 = { c: 3, d: 4 };
const merged = { ...obj1, ...obj2 };

// Template literals
const name = "Alice";
const age = 25;
const message = `Hello, my name is ${name} and I'm ${age} years old.`;

// Classes
class Person {
    constructor(name, age) {
        this.name = name;
        this.age = age;
    }
    
    greet() {
        return `Hello, I'm ${this.name}`;
    }
    
    static species() {
        return "Homo sapiens";
    }
}

class Student extends Person {
    constructor(name, age, grade) {
        super(name, age);
        this.grade = grade;
    }
    
    study() {
        return `${this.name} is studying`;
    }
}

// Modules (ES6 modules)
// export const PI = 3.14159;
// export default function calculate() { ... }
// import calculate, { PI } from './math.js';</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Modern JavaScript Playground</h3>
                <div class="js-playground">
                    <div>
                        <h4>Try Modern Features:</h4>
                        <textarea class="code-editor" id="modern-editor">// Try modern JavaScript features
class Calculator {
    constructor() {
        this.history = [];
    }
    
    add(a, b) {
        const result = a + b;
        this.history.push(`${a} + ${b} = ${result}`);
        return result;
    }
    
    getHistory() {
        return this.history;
    }
}

const calc = new Calculator();
console.log(calc.add(5, 3));
console.log(calc.add(10, 7));
console.log("History:", calc.getHistory());

// Destructuring and spread
const numbers = [1, 2, 3, 4, 5];
const [first, second, ...others] = numbers;
console.log("First:", first, "Others:", others);</textarea>
                        <button onclick="runModernCode()">Run Code</button>
                    </div>
                    <div>
                        <h4>Console Output:</h4>
                        <div class="console-output" id="modern-output"></div>
                    </div>
                </div>
            </div>
        </section>

        <section class="resources" id="resources">
            <h2>Additional Resources</h2>
            <div class="resource-grid">
                <div class="resource-card">
                    <h3>📖 Official Documentation</h3>
                    <ul>
                        <li><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript" target="_blank">MDN JavaScript Reference</a></li>
                        <li><a href="https://tc39.es/ecma262/" target="_blank">ECMAScript Specification</a></li>
                        <li><a href="https://javascript.info/" target="_blank">JavaScript.info</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>🛠️ Tools & Playgrounds</h3>
                    <ul>
                        <li><a href="https://codepen.io/" target="_blank">CodePen</a></li>
                        <li><a href="https://jsfiddle.net/" target="_blank">JSFiddle</a></li>
                        <li><a href="https://replit.com/" target="_blank">Replit</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>📚 Learning Resources</h3>
                    <ul>
                        <li><a href="https://eloquentjavascript.net/" target="_blank">Eloquent JavaScript</a></li>
                        <li><a href="https://github.com/getify/You-Dont-Know-JS" target="_blank">You Don't Know JS</a></li>
                        <li><a href="https://javascript30.com/" target="_blank">JavaScript30</a></li>
                    </ul>
                </div>
            </div>
        </section>
    </main>

    <script src="assets/js/manual-scripts.js"></script>
    <script>
        // Console simulation functions
        function createConsole(outputId) {
            const output = document.getElementById(outputId);
            return {
                log: (...args) => {
                    const message = args.map(arg => 
                        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                    ).join(' ');
                    output.textContent += message + '\n';
                },
                clear: () => {
                    output.textContent = '';
                }
            };
        }
        
        function runVariableCode() {
            const code = document.getElementById('variable-editor').value;
            const console = createConsole('variable-output');
            console.clear();
            
            try {
                // Create a function to execute the code with our custom console
                const func = new Function('console', code);
                func(console);
            } catch (error) {
                console.log('Error: ' + error.message);
            }
        }
        
        function runFunctionCode() {
            const code = document.getElementById('function-editor').value;
            const console = createConsole('function-output');
            console.clear();
            
            try {
                const func = new Function('console', code);
                func(console);
            } catch (error) {
                console.log('Error: ' + error.message);
            }
        }
        
        function runObjectCode() {
            const code = document.getElementById('object-editor').value;
            const console = createConsole('object-output');
            console.clear();
            
            try {
                const func = new Function('console', code);
                func(console);
            } catch (error) {
                console.log('Error: ' + error.message);
            }
        }
        
        function runModernCode() {
            const code = document.getElementById('modern-editor').value;
            const console = createConsole('modern-output');
            console.clear();
            
            try {
                const func = new Function('console', code);
                func(console);
            } catch (error) {
                console.log('Error: ' + error.message);
            }
        }
        
        // Initialize with default output
        document.addEventListener('DOMContentLoaded', function() {
            runVariableCode();
            runFunctionCode();
            runObjectCode();
            runModernCode();
        });
    </script>
</body>
</html>
